export const mediaLibrarySections = [
    {
        key: "latestUploads",
        payload: { limit: 5, orderBy: "dateOfRecording", order: "desc" },
    },
    {
        key: "popular",
        payload: {
            limit: 5,
            orderBy: "dateOfRecording",
            order: "desc",
        },
    },
    {
        key: "latestEnglish",
        payload: {
            limit: 5,
            orderBy: "dateOfRecording",
            order: "desc",
            language: "English",
        },
    },
    {
        key: "hindi",
        payload: {
            limit: 5,
            orderBy: "dateOfRecording",
            order: "desc",
            language: "हिन्दी",
        },
    },
    {
        key: "bangla",
        payload: {
            limit: 5,
            orderBy: "dateOfRecording",
            order: "desc",
            language: "বাংলা",
        },
    },
    {
        key: "bhgavadGita",
        payload: {
            limit: 5,
            orderBy: "dateOfRecording",
            order: "desc",
            category: "Bhagavad-gītā",
        },
    },
    {
        key: "shrimadBhagavatam",
        payload: {
            limit: 5,
            orderBy: "dateOfRecording",
            order: "desc",
            category: "Śrīmad-Bhāgavatam",
        },
    },
    {
        key: "caitanyaCaritamrta",
        payload: {
            limit: 5,
            orderBy: "dateOfRecording",
            order: "desc",
            category: "Śrī Caitanya-caritāmṛta",
        },
    },
    {
        key: "bhajans",
        payload: {
            limit: 5,
            orderBy: "dateOfRecording",
            order: "desc",
            category: "Bhajans",
        },
    },
    {
        key: "visnuSahasranama",
        payload: {
            limit: 5,
            orderBy: "dateOfRecording",
            order: "desc",
            category: "Viṣṇu-sahasranāma",
        },
    },
];

export const playbackModes = [
    { value: 1, label: "Audio only (all lectures)" },
    { value: 2, label: "Only lectures with video" }
]

export const sortByOptions = [
    { value: 1, label: "Default view" },
    { value: 2, label: "Duration (low to high)" },
    { value: 3, label: "Duration (high to low)" },
    { value: 4, label: "Listen (low to high)" },
    { value: 5, label: "Listen (high to low)" },
    { value: 6, label: "Oldest first" },
    { value: 7, label: "Latest first" },
    { value: 8, label: "Alphabetically (A - Z)" },
    { value: 9, label: "Alphabetically (Z - A)" },
]

export const playlistSortByOptions = [
    { value: 1, label: "Default view" },
    { value: 2, label: "Alphabetically (A - Z)" },
    { value: 3, label: "Alphabetically (Z - A)" },
]

export const playlistShowOptions = [
    { value: 1, label: "All" },
    { value: 2, label: "Private" },
    { value: 3, label: "My Public" },
    { value: 4, label: "All Public" }
]

export const historyPeriodOptions = [
    { value: 1, label: "All History" },
    { value: 2, label: "This Week" },
    { value: 3, label: "This Month" }
]

export const popularPeriodOptions = [
    { value: "allTime", label: "All Time" },
    { value: "thisWeek", label: "This Week" },
    { value: "thisMonth", label: "This Month" },
    { value: "lastWeek", label: "Last Week" },
    { value: "lastMonth", label: "Last Month" },
]

export const filterOptions = [
    { value: "LANGUAGE", label: "Languages" },
    { value: "COUNTRY", label: "Countries" },
    { value: "PLACE", label: "Place" },
    { value: "YEAR", label: "Years" },
    { value: "MONTH", label: "Month" },
    { value: "CATEGORY", label: "Categories" },
    { value: "TRANSLATION", label: "Translation" },
]

export const bookFilterOptions = [
    { value: "BOOKS", label: "Books" },
]

export const LANGUAGE = [
    { value: "English", label: "English" },
    { value: "বাংলা", label: "বাংলা" },
    { value: "हिन्दी", label: "हिन्दी" },
]

export const COUNTRY = [
    { value: "Bangladesh", label: "Bangladesh" },
    { value: "Canada", label: "Canada" },
    { value: "Croatia", label: "Croatia" },
    { value: "Czech Republic", label: "Czech Republic" },
    { value: "Finland", label: "Finland" },
    { value: "Germany", label: "Germany" },
    { value: "Hungary", label: "Hungary" },
    { value: "India", label: "India" },
    { value: "Ireland", label: "Ireland" },
    { value: "Poland", label: "Poland" },
    { value: "Russia", label: "Russia" },
    { value: "Slovenia", label: "Slovenia" },
    { value: "Sri Lanka", label: "Sri Lanka" },
    { value: "Sweden", label: "Sweden" },
    { value: "UK", label: "UK" },
    { value: "USA", label: "USA" }
]

export const PLACE = [
    { value: "Ahmedabad, Gujarat", label: "Ahmedabad, Gujarat" },
    { value: "Akkaraipattu, Tamil Nadu", label: "Akkaraipattu, Tamil Nadu" },
    { value: "Albuquerque, New Mexico", label: "Albuquerque, New Mexico" },
    { value: "Ambur, Tamil Nadu", label: "Ambur, Tamil Nadu" },
    { value: "Amona, Goa", label: "Amona, Goa" },
    { value: "Ankleshwar, Gujarat", label: "Ankleshwar, Gujarat" },
    { value: "Bangalore, Karnataka", label: "Bangalore, Karnataka" },
    { value: "Baroda/Vadodara, Gujarat", label: "Baroda/Vadodara, Gujarat" },
    { value: "Batticaloa, Sri Lanka", label: "Batticaloa, Sri Lanka" },
    { value: "Bhaktivedanta Manor, London", label: "Bhaktivedanta Manor, London" },
    { value: "Bharuch, Gujarat", label: "Bharuch, Gujarat" },
    { value: "Bhimavaram, Andhra Pradesh", label: "Bhimavaram, Andhra Pradesh" },
    { value: "Bhopal, Madhya Pradesh", label: "Bhopal, Madhya Pradesh" },
    { value: "Bilimora, Gujarat", label: "Bilimora, Gujarat" },
    { value: "Brahmanbaria, Bangladesh", label: "Brahmanbaria, Bangladesh" },
    { value: "Brazina", label: "Brazina" },
    { value: "Cakovec, Croatia", label: "Cakovec, Croatia" },
    { value: "Chattogram, Bangladesh", label: "Chattogram, Bangladesh" },
    { value: "Chennai, Tamil Nadu", label: "Chennai, Tamil Nadu" },
    { value: "Chicago, Illinois", label: "Chicago, Illinois" },
    { value: "Chittagong, Bangladesh", label: "Chittagong, Bangladesh" },
    { value: "Coimbatore, Tamil Nadu", label: "Coimbatore, Tamil Nadu" },
    { value: "Comilla, Bangladesh", label: "Comilla, Bangladesh" },
    { value: "Corvallis, Oregon", label: "Corvallis, Oregon" },
    { value: "Dallas, Texas", label: "Dallas, Texas" }
]


export const YEAR = [
    { value: "2025", label: "2025" },
    { value: "2024", label: "2024" },
    { value: "2023", label: "2023" },
    { value: "2022", label: "2022" },
    { value: "2021", label: "2021" },
    { value: "2020", label: "2020" },
    { value: "2019", label: "2019" },
    { value: "2018", label: "2018" },
    { value: "2017", label: "2017" },
    { value: "2016", label: "2016" },
    { value: "2015", label: "2015" },
    { value: "2014", label: "2014" },
    { value: "2013", label: "2013" },
    { value: "2012", label: "2012" },
    { value: "2011", label: "2011" },
    { value: "2010", label: "2010" },
    { value: "2009", label: "2009" },
    { value: "2008", label: "2008" },
    { value: "2007", label: "2007" },
    { value: "2006", label: "2006" },
    { value: "2005", label: "2005" },
    { value: "2004", label: "2004" },
    { value: "2003", label: "2003" },
    { value: "2002", label: "2002" },
    { value: "2001", label: "2001" },
    { value: "2000", label: "2000" },
    { value: "1999", label: "1999" },
    { value: "1998", label: "1998" },
    { value: "1997", label: "1997" },
    { value: "1996", label: "1996" },
    { value: "1995", label: "1995" },
    { value: "1994", label: "1994" },
    { value: "1993", label: "1993" }
]

export const MONTH = [
    { value: "01", label: "January" },
    { value: "02", label: "February" },
    { value: "03", label: "March" },
    { value: "04", label: "April" },
    { value: "05", label: "May" },
    { value: "06", label: "June" },
    { value: "07", label: "July" },
    { value: "08", label: "August" },
    { value: "09", label: "September" },
    { value: "10", label: "October" },
    { value: "11", label: "November" },
    { value: "12", label: "December" }
]

export const CATEGORY = [
    { value: "ASK BVKS", label: "ASK BVKS" },
    { value: "Assorted Lectures", label: "Assorted Lectures" },
    { value: "Bhagavad-gītā", label: "Bhagavad-gītā" },
    { value: "Bhajans", label: "Bhajans" },
    { value: "Conversations", label: "Conversations" },
    { value: "Seminars", label: "Seminars" },
    { value: "Śrīmad-Bhāgavatam", label: "Śrīmad-Bhāgavatam" },
    { value: "Śrī Caitanya-caritāmṛta", label: "Śrī Caitanya-caritāmṛta" },
    { value: "Viṣṇu-sahasranāma", label: "Viṣṇu-sahasranāma" }
]

export const TRANSLATION = [
    { value: "Hrvatski", label: "Hrvatski (Croatian)" },
    { value: "Portuguese", label: "Portuguese" },
    { value: "Slovenščina", label: "Slovenščina (Slovenian)" },
    { value: "Český", label: "Český (Czech)" },
    { value: "Русский", label: "Русский (Russian)" },
    { value: "हिन्दी", label: "हिन्दी (Hindi)" },
    { value: "বাংলা", label: "বাংলা (Bengali)" },
    { value: "தமிழ்", label: "தமிழ் (Tamil)" },
    { value: "తెలుగు", label: "తెలుగు (Telugu)" }
]

export const PlaylistType = [
    { value: "PRIVATE", label: "Private" },
    { value: "PUBLIC", label: "Public" }
]

export const BOOKS = [
    { value: "a-message-to-the-youth-of-india", label: "A Message to the Youth of India" },
    { value: "brahmacarya-in-krsna-consciousness", label: "Brahmacarya in Kṛṣṇa Consciousness" },
    { value: "glimpses-of-traditional-indian-life", label: "Glimpses of Traditional Indian Life" },
    { value: "jaya-srila-prabhupada", label: "Jaya Śrīla Prabhupāda!" },
    { value: "lekha-mala-a-garland-of-writings", label: "Lekha Mala a garland of writings" },
    { value: "mothers-and-masters", label: "Mothers & Masters" },
    { value: "my-memories-of-srila-prabhupada", label: "My Memories of Śrila Prabhupāda" },
    { value: "on-pilgrimage-in-holy-india", label: "On Pilgrimage in Holy India" },
    { value: "on-speaking-strongly-in-srila-prabhupadas-service", label: "On Speaking Strongly in Śrila Prabhupāda's Service" },
    { value: "patropadesa-volume-1", label: "Patropadesa Volume 1" },
    { value: "patropadesa-volume-2", label: "Patropadesa Volume 2" },
    { value: "r-a-m-a-y-a-a", label: "R ā m ā y a ṇ a" },
    { value: "the-story-of-rasikananda", label: "The Story of Rasikānanda" },
    { value: "sri-bhaktisiddhanta-vaibhava-volume-1", label: "Śri Bhaktisiddhānta Vaibhava Volume 1" },
    { value: "sri-bhaktisiddhanta-vaibhava-volume-2", label: "Śri Bhaktisiddhānta Vaibhava Volume 2" },
    { value: "sri-bhaktisiddhanta-vaibhava-volume-3", label: "Śri Bhaktisiddhānta Vaibhava Volume 3" },
    { value: "sri-caitanya-mahaprabhu", label: "Śrī Caitanya Mahāprabhu" },
    { value: "sri-vamsidasa-babaji", label: "Śrī Vaṁśīdāsa Bābāji" }
]
