importScripts("https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js");
importScripts("https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js");

firebase.initializeApp({
    apiKey: "AIzaSyCXFbWCZyoeHL8hxbojbexI-kRKPDcirj8",
    authDomain: "bvks-ee662.firebaseapp.com",
    projectId: "bvks-ee662",
    storageBucket: "bvks-ee662.appspot.com",
    messagingSenderId: "184985407900",
    appId: "1:184985407900:web:c3632eef8c2104e727b859",
});

const messaging = firebase.messaging();

// ✅ Manually handle background notifications
messaging.onBackgroundMessage((payload) => {
    console.log("Received background message:", payload);

    if (!payload.notification) {
        console.warn("No notification payload found, skipping.");
        return;
    }

    const notificationTitle = payload.notification.title;
    const notificationOptions = {
        body: payload.notification.body,
        icon: payload.notification.icon,
        data: { url: payload.fcmOptions?.link || "/" },
    };

    
    // ✅ Prevent Duplicate Notifications
    self.registration.getNotifications().then((existingNotifications) => {
        const alreadyExists = existingNotifications.some(
            (n) => n.title === notificationTitle && n.body === notificationOptions.body
        );
        if (!alreadyExists) {
            console.log("Showing notification:", notificationTitle);
            self.registration.showNotification(notificationTitle, notificationOptions).then(() => {
                console.log("Notification displayed successfully");
            }). catch((error) => {
                console.error("Error showing notification:", error);
            });
        } else {
            console.log("Duplicate notification prevented:", notificationTitle);
        }
    });
});