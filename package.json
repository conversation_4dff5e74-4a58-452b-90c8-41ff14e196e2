{"name": "bvks", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@headlessui/react": "^2.2.4", "@mui/icons-material": "^6.4.4", "@mui/material": "^6.4.10", "@mui/system": "^5.16.14", "@types/leaflet": "^1.9.20", "antd": "^5.23.3", "aos": "^2.3.4", "apexcharts": "^4.7.0", "axios": "^1.9.0", "firebase": "^11.3.1", "framer-motion": "^12.12.1", "idb": "^8.0.3", "leaflet": "^1.9.4", "next": "14.2.7", "react": "^18", "react-apexcharts": "^1.7.0", "react-dom": "^18", "react-icons": "^5.5.0", "react-infinite-scroll-component": "^6.1.0", "react-leaflet": "^4.2.1", "react-loading-skeleton": "^3.5.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}