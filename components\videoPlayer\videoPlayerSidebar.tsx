"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useVideoContext } from "@/src/context/video.context";
import { fetchLectures } from "@/src/api/mediaLibrary.api";
import { formatSecondsToHHMMSS } from "@/src/utils/timeFormat";
import { Poppins } from "next/font/google";
import Image from "next/image";
import appConfig from "@/src/config/apps";
import { BsPlayCircle } from "react-icons/bs";

const poppins = Poppins({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

interface RelatedVideo {
    id: string;
    title: string[];
    thumbnail?: string;
    thumbnailUrl?: string;
    category: string[];
    length: number;
    resources?: {
        videos?: any[];
    };
}

const VideoPlayerSidebar = () => {
    const router = useRouter();
    const { currentVideo, isPlaying } = useVideoContext();
    const [relatedVideos, setRelatedVideos] = useState<RelatedVideo[]>([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchRelatedVideos = async () => {
            try {
                setLoading(true);

                // Fetch lectures with videos
                const lectures = await fetchLectures({
                    limit: 20,
                    orderBy: "dateOfRecording",
                    order: "desc",
                });

                // Filter for lectures with videos and exclude current video
                const videosWithContent = lectures.filter(
                    (lecture: any) =>
                        lecture.resources?.videos?.length > 0 &&
                        lecture.id !== currentVideo.id
                );

                // Take first 10 related videos
                setRelatedVideos(videosWithContent.slice(0, 10));
            } catch (error) {
                console.error("Error fetching related videos:", error);
            } finally {
                setLoading(false);
            }
        };

        if (currentVideo.id) {
            fetchRelatedVideos();
        }
    }, [currentVideo.id]);

    const handleVideoClick = (video: RelatedVideo) => {
        // Navigate to the new video page
        router.push(`/video/${video.id}`);
    };

    const getTitle = (video: RelatedVideo) => {
        return Array.isArray(video.title) ? video.title.join(" ") : video.title;
    };

    const getCategory = (video: RelatedVideo) => {
        return Array.isArray(video.category)
            ? video.category.join(", ")
            : video.category;
    };

    const getThumbnail = (video: RelatedVideo) => {
        return (
            video.thumbnail ||
            video.thumbnailUrl ||
            appConfig.defaultLectureThumbnail
        );
    };

    if (loading) {
        return (
            <div className={`p-4 ${poppins.className}`}>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Related Lectures
                </h3>
                <div className="space-y-3">
                    {[...Array(5)].map((_, index) => (
                        <div key={index} className="animate-pulse">
                            <div className="flex gap-3">
                                <div className="w-32 h-20 bg-gray-200 rounded"></div>
                                <div className="flex-1">
                                    <div className="h-4 bg-gray-200 rounded mb-2"></div>
                                    <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        );
    }

    return (
        <div className={`${poppins.className}`}>
            {/* Queue Section */}
            <div className="queue-section">
                <div className="queue-header">
                    <h3 className="text-lg font-semibold">Up Next</h3>
                </div>

                <div className="overflow-y-auto scrollbar h-[calc(100vh-170px)]">
                    {loading ? (
                        <div className="p-4 space-y-3">
                            {[...Array(3)].map((_, index) => (
                                <div key={index} className="animate-pulse">
                                    <div className="flex gap-3">
                                        <div className="w-28 h-16 bg-gray-200 rounded"></div>
                                        <div className="flex-1">
                                            <div className="h-4 bg-gray-200 rounded mb-2"></div>
                                            <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="p-2">
                            {/* Currently Playing Video */}
                            {currentVideo && (
                                <div className="flex gap-3 p-2 rounded-lg bg-gray-50 mb-4">
                                    {/* Thumbnail with Wave Animation */}
                                    <div className="relative flex-shrink-0 video-thumbnail">
                                        <img
                                            src={getThumbnail(currentVideo)}
                                            alt={getTitle(currentVideo)}
                                            width={112}
                                            height={64}
                                            className="w-36 h-20 object-cover rounded"
                                        />
                                        {/* Wave Animation Overlay */}
                                        <div
                                            className="absolute inset-0 bg-center bg-no-repeat bg-contain opacity-30"
                                        />

                                        {/* Now Playing Badge */}
                                        <div className="absolute top-0 w-full h-full flex justify-center items-center bg-black bg-opacity-30 text-white text-xs px-2 py-1">
                                            {isPlaying ? (
                                                <img
                                                    src="/images/wave-animation-1.gif"
                                                    alt="Loading..."
                                                    className="w-[80%] h-[80%]"
                                                />
                                            ) : (
                                                <BsPlayCircle
                                                    style={{
                                                        color: "white",
                                                        fontSize: 20,
                                                    }}
                                                />
                                            )}
                                        </div>
                                    </div>

                                    {/* Video Info */}
                                    <div className="flex-1 min-w-0">
                                        <h4 className="text-sm font-bold text-primary line-clamp-2 leading-tight mb-1">
                                            {getTitle(currentVideo)}
                                        </h4>
                                        <p className="text-xs text-gray-600 line-clamp-1">
                                            {getCategory(currentVideo)}
                                        </p>
                                    </div>
                                </div>
                            )}

                            {/* Related Videos */}
                            {relatedVideos.map((video) => (
                                <div
                                    key={video.id}
                                    onClick={() => handleVideoClick(video)}
                                    className="flex gap-3 p-2 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors group"
                                >
                                    {/* Thumbnail */}
                                    <div className="relative flex-shrink-0 video-thumbnail">
                                        <img
                                            src={getThumbnail(video)}
                                            alt={getTitle(video)}
                                            width={112}
                                            height={64}
                                            className="w-36 h-20 object-cover rounded"
                                        />
                                        {/* Duration overlay */}
                                        <div className="absolute bottom-1 right-1 bg-black bg-opacity-80 text-white text-xs px-2 py-1 rounded font-medium">
                                            {formatSecondsToHHMMSS(
                                                video.length
                                            )}
                                        </div>
                                    </div>

                                    {/* Video Info */}
                                    <div className="flex-1 min-w-0">
                                        <h4 className="text-sm font-medium text-gray-900 line-clamp-2 leading-tight mb-1 group-hover:text-primary">
                                            {getTitle(video)}
                                        </h4>
                                        <p className="text-xs text-gray-600 line-clamp-1">
                                            {getCategory(video)}
                                        </p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default VideoPlayerSidebar;
