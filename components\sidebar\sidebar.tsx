"use client";
import React, { useEffect, useState } from "react";
import { Poppins } from "next/font/google";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { useSidebarContext } from "@/src/context/sidebar.context";
import { Avatar, Button, Popover, Tooltip } from "antd";
import LogoutIcon from "@mui/icons-material/Logout";
import LogoutModal from "../Modal/logoutModal";
import { useSearchContext } from "@/src/context/search.context";
import IconHistory from "../ui/iconHistory";
import IconMediaLibrary from "../ui/iconMediaLibrary";
import IconPopular from "../ui/iconPopular";
import IconFavorite from "../ui/iconFavorite";
import IconPlaylists from "../ui/iconPlaylists";
import IconStatistics from "../ui/iconStatistics";
import IconInfo from "../ui/iconInfo";
import IconDonate from "../ui/iconDonate";
import IconSettings from "../ui/iconSettings";
import Image from "next/image";
import { useFilterContext } from "@/src/context/filter.context";
import IconItinerary from "../ui/iconItinerary";
import IconGallery from "../ui/iconGallery";

const poppins = Poppins({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
});

const Sidebar = () => {
  const path = usePathname();
  const { resetSearchStates } = useSearchContext();
  const { resetFilters } = useFilterContext();

  const [isLogoutModalOpen, setIsLogoutModalOpen] = useState(false);
  const { isCollapsed, setIsTabChangeLoading } = useSidebarContext();
  const [selectedItem, setSelectedItem] = useState("");
  const [email, setEmail] = useState("");

  useEffect(() => {
    const email = localStorage.getItem("email");
    if (email) {
      setEmail(email);
    }
  }, []);

  useEffect(() => {
    const page = path.split("/")[1];

    if (page && page !== "") {
      setSelectedItem(page);
    }
  }, [isCollapsed, path]);

  const handleButtonClick = (button: any) => {
    setSelectedItem(button);
    if (selectedItem !== button || path.split("/")[1] !== button) {
      resetSearchStates();
      resetFilters();
      setIsTabChangeLoading(true);
    }
  };

  if (!isCollapsed) {
    return (
      <div
        className={`absolute lg:relative shadow-xl lg:shadow-none bg-white z-50 w-[230px] h-full pb-40 lg:pb-0 overflow-y-auto scrollbar-hide border-r border-[#e0e0e0] ${poppins.className} lg:transition-all duration-500 ease-in-out`}
        // data-aos="fade-right"
      >
        <div className="flex flex-col gap-1 my-6">
          <div className="md:hidden px-4 flex gap-4 items-center mb-4">
            <Avatar
              style={{
                backgroundColor: "#E0E0E0",
                color: "#343A40",
                verticalAlign: "middle",
              }}
              className="font-bold"
              size={40}
            >
              {email.toUpperCase().slice(0, 2)}
            </Avatar>
            <Popover
              placement="bottom"
              title={
                <h1 className="text-[14px] p-2 cursor-default tracking-wide font-[500]">
                  {email}
                </h1>
              }
              trigger={["click"]}
              content={false}
              arrow={false}
            >
              <h1 className="w-[calc(100%-56px)] text-black text-[14px] py-1 cursor-default tracking-wide font-[500] truncate">
                {email}
              </h1>
            </Popover>
          </div>

          {/* Media Library */}
          <Link
            href={"/media-library"}
            className={`w-full transition-all flex items-center gap-4 cursor-pointer px-8 py-2 hover:bg-primary-hover`}
            onClick={() => {
              handleButtonClick("media-library");
            }}
            // data-aos="fade-right"
          >
            {selectedItem === "media-library" ? (
              <IconMediaLibrary color="var(--primary-color)" />
            ) : (
              <IconMediaLibrary color="#919191" />
            )}
            <p
              className={`${
                selectedItem === "media-library"
                  ? "text-primary font-[600]"
                  : "text-text-primary font-[400]"
              } text-[14px] leading-5`}
            >
              Media&nbsp;Library
            </p>
          </Link>

          {/* Popular */}
          <Link
            href={"/popular"}
            className={`w-full transition-all flex items-center gap-4 cursor-pointer px-8 py-2 hover:bg-primary-hover`}
            onClick={() => {
              handleButtonClick("popular");
            }}
            // data-aos="fade-right"
          >
            {selectedItem === "popular" ? (
              <IconPopular color="var(--primary-color)" />
            ) : (
              <IconPopular color="#919191" />
            )}
            <p
              className={`${
                selectedItem === "popular"
                  ? "text-primary font-[600]"
                  : "text-text-primary font-[400]"
              } text-[14px] leading-5`}
            >
              Popular
            </p>
          </Link>

          {/* Favorites */}
          <Link
            href={"/favorites"}
            className={`w-full transition-all flex items-center gap-4 cursor-pointer px-8 py-2 hover:bg-primary-hover`}
            onClick={() => {
              handleButtonClick("favorites");
            }}
            // data-aos="fade-right"
          >
            {selectedItem === "favorites" ? (
              <IconFavorite color="var(--primary-color)" />
            ) : (
              <IconFavorite color="#919191" />
            )}
            <p
              className={`${
                selectedItem === "favorites"
                  ? "text-primary font-[600]"
                  : "text-text-primary font-[400]"
              } text-[14px] leading-5`}
            >
              Favorites
            </p>
          </Link>

          {/* Playlists */}
          <Link
            href={"/playlists"}
            className={`w-full transition-all flex items-center gap-4 cursor-pointer px-8 py-2 hover:bg-primary-hover`}
            onClick={() => {
              handleButtonClick("playlists");
            }}
            // data-aos="fade-right"
          >
            {selectedItem === "playlists" ? (
              <IconPlaylists color="var(--primary-color)" />
            ) : (
              <IconPlaylists color="#919191" />
            )}
            <p
              className={`${
                selectedItem === "playlists"
                  ? "text-primary font-[600]"
                  : "text-text-primary font-[400]"
              } text-[14px] leading-5`}
            >
              Playlists
            </p>
          </Link>

          {/* <Link
            href={"/itinerary"}
            className={`w-full transition-all flex items-center gap-4 cursor-pointer px-8 py-2 hover:bg-primary-hover`}
            onClick={() => {
              handleButtonClick("itinerary");
            }}
            // data-aos="fade-right"
          >
            {selectedItem === "itinerary" ? (
              <Image
                width={24}
                height={24}
                className="w-[24px] h-[24px]"
                src={"/images/sidebar/itinerarySelected.svg"}
                alt=""
              />
            ) : (
              <Image
                width={24}
                height={24}
                className="w-[24px] h-[24px]"
                src={"/images/sidebar/itinerary.svg"}
                alt=""
              />
            )}
            <p
              className={`${
                selectedItem === "itinerary"
                  ? "text-primary font-[600]"
                  : "text-text-primary font-[400]"
              } text-[14px] leading-5 mt-1`}
            >
              Itinerary
            </p>
          </Link>

          <Link
            href={"/gallery"}
            className={`w-full transition-all flex items-center gap-4 cursor-pointer px-8 py-2 hover:bg-primary-hover`}
            onClick={() => {
              handleButtonClick("gallery");
            }}
            // data-aos="fade-right"
          >
            {selectedItem === "gallery" ? (
              <IconGallery color="var(--primary-color)" />
            ) : (
              <IconGallery color="#919191" />
            )}
            <p
              className={`${
                selectedItem === "gallery"
                  ? "text-primary font-[600]"
                  : "text-text-primary font-[400]"
              } text-[14px] leading-5 mt-1`}
            >
              Gallery
            </p>
          </Link> */}

          {/* History */}
          <Link
            href={"/history"}
            className={`w-full transition-all flex items-center gap-4 cursor-pointer px-8 py-2 hover:bg-primary-hover`}
            onClick={() => {
              handleButtonClick("history");
            }}
            // data-aos="fade-right"
          >
            {selectedItem === "history" ? (
              <IconHistory color="var(--primary-color)" />
            ) : (
              <IconHistory color="#919191" />
            )}
            <p
              className={`${
                selectedItem === "history"
                  ? "text-primary font-[600]"
                  : "text-text-primary font-[400]"
              } text-[14px] leading-5`}
            >
              History
            </p>
          </Link>

          {/* Statistics */}
          <Link
            href={"/statistics"}
            className={`w-full transition-all flex items-center gap-4 cursor-pointer px-8 py-2 hover:bg-primary-hover`}
            onClick={() => {
              handleButtonClick("statistics");
            }}
            // data-aos="fade-right"
          >
            {selectedItem === "statistics" ? (
              <IconStatistics color="var(--primary-color)" />
            ) : (
              <IconStatistics color="#919191" />
            )}
            <p
              className={`${
                selectedItem === "statistics"
                  ? "text-primary font-[600]"
                  : "text-text-primary font-[400]"
              } text-[14px] leading-5`}
            >
              Statistics
            </p>
          </Link>

          {/* Knowledge base */}
          <div className="flex flex-col gap-2 my-1">
            <hr className="border-[1.5px] mx-4" />

            <Link
              href={"/knowledge-base"}
              // data-aos="fade-right"
              className={`w-full transition-all flex items-center gap-4 cursor-pointer px-4 py-[2px] hover:bg-primary-hover`}
              onClick={() => {
                handleButtonClick("knowledge-base");
              }}
            >
              <Image
                width={38}
                height={38}
                className="w-[38px] h-[38px]"
                src={"/images/sidebar/knowledge_base.svg"}
                alt=""
              />
              <p
                className={`${
                  selectedItem === "knowledge-base"
                    ? "text-primary font-[600]"
                    : "text-text-primary font-[400]"
                } text-[14px] leading-5`}
              >
                Knowledge&nbsp;Base
              </p>
            </Link>

            <hr className="border-[1.5px] mx-4" />
          </div>

          {/* About */}
          <Link
            href={"/about"}
            className={`w-full transition-all flex items-center gap-4 cursor-pointer px-8 py-2 hover:bg-primary-hover`}
            onClick={() => {
              handleButtonClick("about");
            }}
            // data-aos="fade-right"
          >
            {selectedItem === "about" ? (
              <IconInfo color="var(--primary-color)" />
            ) : (
              <IconInfo color="#919191" />
            )}
            <p
              className={`${
                selectedItem === "about"
                  ? "text-primary font-[600]"
                  : "text-text-primary font-[400]"
              } text-[14px] leading-5 mt-1`}
            >
              About
            </p>
          </Link>

          {/* Donate */}
          <Link
            href={"/donate"}
            className={`w-full transition-all flex items-center gap-4 cursor-pointer px-8 py-2 hover:bg-primary-hover`}
            onClick={() => {
              handleButtonClick("donate");
            }}
            // data-aos="fade-right"
          >
            {selectedItem === "donate" ? (
              <IconDonate color="var(--primary-color)" />
            ) : (
              <IconDonate color="#919191" />
            )}
            <p
              className={`${
                selectedItem === "donate"
                  ? "text-primary font-[600]"
                  : "text-text-primary font-[400]"
              } text-[14px] leading-5 mt-1`}
            >
              Donate
            </p>
          </Link>

          {/* Settings */}
          <Link
            href={"/settings"}
            className={`w-full transition-all flex items-center gap-4 cursor-pointer px-8 py-2 hover:bg-primary-hover`}
            onClick={() => {
              handleButtonClick("settings");
            }}
            // data-aos="fade-right"
          >
            {selectedItem === "settings" ? (
              <IconSettings color="var(--primary-color)" />
            ) : (
              <IconSettings color="#919191" />
            )}
            <p
              className={`${
                selectedItem === "settings"
                  ? "text-primary font-[600]"
                  : "text-text-primary font-[400]"
              } text-[14px] leading-5 mt-1`}
            >
              Settings
            </p>
          </Link>

          <Button
            onClick={() => setIsLogoutModalOpen(true)}
            className="md:hidden mt-4 text-[15px] h-[44px] border-none rounded-[10px] mx-4 flex gap-2 items-center font-[500] hover:opacity-80"
            style={{
              background: "#fd7e14",
              color: "white",
            }}
          >
            <LogoutIcon /> <p>Sign Out</p>
          </Button>
          <LogoutModal
            isModalOpen={isLogoutModalOpen}
            setIsModalOpen={setIsLogoutModalOpen}
          />
        </div>
      </div>
    );
  }

  return (
    <div
      className={`absolute lg:relative w-0 lg:w-[68px] h-full border-r border-[#e0e0e0] ${poppins.className} transition-all duration-500 ease-in-out`}
    >
      <div className="hidden lg:flex flex-col gap-1 my-6">
        {/* Media Library */}
        <Tooltip title="Media Library" placement="right">
          <Link
            href={"/media-library"}
            className={`w-full transition-all flex items-center justify-center gap-4 cursor-pointer px-0 py-2 hover:bg-primary-hover`}
            onClick={() => {
              handleButtonClick("media-library");
            }}
          >
            {selectedItem === "media-library" ? (
              <IconMediaLibrary color="var(--primary-color)" />
            ) : (
              <IconMediaLibrary color="#919191" />
            )}
          </Link>
        </Tooltip>

        {/* Popular */}
        <Tooltip title="Popular" placement="right">
          <Link
            href={"/popular"}
            className={`w-full transition-all flex items-center justify-center gap-4 cursor-pointer px-0 py-2 hover:bg-primary-hover`}
            onClick={() => {
              handleButtonClick("popular");
            }}
          >
            {selectedItem === "popular" ? (
              <IconPopular color="var(--primary-color)" />
            ) : (
              <IconPopular color="#919191" />
            )}
          </Link>
        </Tooltip>

        {/* Favorites */}
        <Tooltip title="Favorites" placement="right">
          <Link
            href={"/favorites"}
            className={`w-full transition-all flex items-center justify-center gap-4 cursor-pointer px-0 py-2 hover:bg-primary-hover`}
            onClick={() => {
              handleButtonClick("favorites");
            }}
          >
            {selectedItem === "favorites" ? (
              <IconFavorite color="var(--primary-color)" />
            ) : (
              <IconFavorite color="#919191" />
            )}
          </Link>
        </Tooltip>

        {/* Playlists */}
        <Tooltip title="Playlists" placement="right">
          <Link
            href={"/playlists"}
            className={`w-full transition-all flex items-center justify-center gap-4 cursor-pointer px-0 py-2 hover:bg-primary-hover`}
            onClick={() => {
              handleButtonClick("playlists");
            }}
          >
            {selectedItem === "playlists" ? (
              <IconPlaylists color="var(--primary-color)" />
            ) : (
              <IconPlaylists color="#919191" />
            )}
          </Link>
        </Tooltip>

        {/* History */}
        <Tooltip title="History" placement="right">
          <Link
            href={"/history"}
            className={`w-full transition-all flex items-center justify-center gap-4 cursor-pointer px-0 py-2 hover:bg-primary-hover`}
            onClick={() => {
              handleButtonClick("history");
            }}
          >
            {selectedItem === "history" ? (
              <IconHistory color="var(--primary-color)" />
            ) : (
              <IconHistory color="#919191" />
            )}
          </Link>
        </Tooltip>

        {/* Statistics */}
        <Tooltip title="Statistics" placement="right">
          <Link
            href={"/statistics"}
            className={`w-full transition-all flex items-center justify-center gap-4 cursor-pointer px-0 py-2 hover:bg-primary-hover`}
            onClick={() => {
              handleButtonClick("statistics");
            }}
          >
            {selectedItem === "statistics" ? (
              <IconStatistics color="var(--primary-color)" />
            ) : (
              <IconStatistics color="#919191" />
            )}
          </Link>
        </Tooltip>

        {/* Knowledge base */}
        <div className="flex flex-col gap-2 my-1">
          <hr className="border-[1.5px] mx-4" />

          <Tooltip title="Knowledge Base" placement="right">
            <Link
              href={"/knowledge-base"}
              className={`w-full transition-all flex items-center justify-center gap-4 cursor-pointer px-0 py-[2px] hover:bg-primary-hover`}
              onClick={() => {
                handleButtonClick("knowledge-base");
              }}
            >
              <Image
                width={38}
                height={38}
                className="w-[38px] h-[38px]"
                src={"/images/sidebar/knowledge_base.svg"}
                alt=""
              />
            </Link>
          </Tooltip>

          <hr className="border-[1.5px] mx-4" />
        </div>

        {/* About */}
        <Tooltip title="About" placement="right">
          <Link
            href={"/about"}
            className={`w-full transition-all flex items-center justify-center gap-4 cursor-pointer px-0 py-2 hover:bg-primary-hover`}
            onClick={() => {
              handleButtonClick("about");
            }}
          >
            {selectedItem === "about" ? (
              <IconInfo color="var(--primary-color)" />
            ) : (
              <IconInfo color="#919191" />
            )}
          </Link>
        </Tooltip>

        {/* Donate */}
        <Tooltip title="Donate" placement="right">
          <Link
            href={"/donate"}
            className={`w-full transition-all flex items-center justify-center gap-4 cursor-pointer px-0 py-2 hover:bg-primary-hover`}
            onClick={() => {
              handleButtonClick("donate");
            }}
          >
            {selectedItem === "donate" ? (
              <IconDonate color="var(--primary-color)" />
            ) : (
              <IconDonate color="#919191" />
            )}
          </Link>
        </Tooltip>

        {/* Settings */}
        <Tooltip title="Settings" placement="right">
          <Link
            href={"/settings"}
            className={`w-full transition-all flex items-center justify-center gap-4 cursor-pointer px-0 py-2 hover:bg-primary-hover`}
            onClick={() => {
              handleButtonClick("settings");
            }}
          >
            {selectedItem === "settings" ? (
              <IconSettings color="var(--primary-color)" />
            ) : (
              <IconSettings color="#919191" />
            )}
          </Link>
        </Tooltip>
      </div>
    </div>
  );
};

export default Sidebar;
