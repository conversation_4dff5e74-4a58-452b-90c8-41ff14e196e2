import { useEffect } from "react";
import { onMessage } from "firebase/messaging";
import { messaging } from "@/src/config/firebase.config";
import { notification } from "antd";

const NotificationListener = () => {
  const [api, contextHolder] = notification.useNotification();

  // Define openNotification outside of the onMessage callback
  const openNotification = (title: string, body: string) => {
    api.open({
      message: title,
      description: body,
      duration: 5,
      placement: "bottomRight",
      style: {
        borderRadius: "12px",
      },
      pauseOnHover: true,
    });
  };

  useEffect(() => {
    const unsubscribe = onMessage(messaging, (payload) => {
      
      console.log("Foreground message received:", payload);
      const { title, body } = payload.notification || {};

      // Directly call openNotification
      if (title && body) {
        openNotification(title, body);
      }
    });

    return () => unsubscribe();
  }, [api]); // Add api to dependencies

  return contextHolder; // Important: Return contextHolder to make notifications work
};

export default NotificationListener;
