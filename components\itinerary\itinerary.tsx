"use client";

import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "react-leaflet";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import { collection, getDocs } from "firebase/firestore";
import { db } from "@/src/config/firebase.config";
import Image from "next/image";
import { Poppins } from "next/font/google";
import {
  FaChevronLeft,
  FaChevronRight,
  FaDownload,
  FaTimes,
} from "react-icons/fa";
import ItineraryCard from "./itineraryCard";

const poppins = Poppins({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
});

const orangeIcon = new L.Icon({
  iconUrl: "/images/itinerary/marker.svg",
  iconSize: [40, 40],
  iconAnchor: [20, 20],
  popupAnchor: [0, -20],
});

const Itinerary = () => {
  const [itinerary, setItinerary] = useState<any[]>([]);
  const [selectedLocation, setSelectedLocation] = useState<any>(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  const handlePrev = () => {
    if (!selectedLocation) return;
    setSelectedImageIndex((prev) => Math.max(prev - 1, 0));
  };

  const handleNext = () => {
    if (!selectedLocation) return;
    setSelectedImageIndex((prev) =>
      Math.min(prev + 1, selectedLocation.images.length - 1)
    );
  };

  const handleClose = () => {
    setSelectedLocation(null);
    setSelectedImageIndex(0);
  };

  useEffect(() => {
    const fetchData = async () => {
      const querySnapshot = await getDocs(collection(db, "itinerary"));
      const items = querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));
      setItinerary(items);
    };
    fetchData();
  }, []);

  const polylinePoints = itinerary.map((item) => [
    item.venue.latitude,
    item.venue.longitude,
  ]);

  console.log("selectedLocation", selectedLocation);
  console.log("selectedImageIndex", selectedImageIndex);

  return (
    <div className={`w-full h-full ${poppins.className} tracking-normal`}>
      {/* Header */}
      <div className="w-full h-[60px] md:h-[84px] flex items-center md:items-end px-4 md:py-3 border-b">
        <h1 className="text-[24px] md:text-[28px] font-[600] leading-8">
          Itinerary
        </h1>
      </div>

      {/* Map */}
      <div
        style={{
          width: "100%",
          height: "calc(100% - 84px)",
          position: "relative",
          zIndex: 50,
        }}
      >
        <MapContainer
          center={[30, 20]}
          zoom={2}
          scrollWheelZoom
          style={{ width: "100%", height: "100%" }}
        >
          <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />

          {/* Polyline connecting stops */}
          {polylinePoints.length > 1 && (
            <Polyline positions={polylinePoints} color="#003F6B" weight={3} />
          )}

          {/* Markers */}
          {itinerary.map((stop) => (
            <Marker
              key={stop.id}
              position={[stop.venue.latitude, stop.venue.longitude]}
              icon={orangeIcon}
            >
              <Tooltip permanent direction="top" offset={[0, -20]} opacity={1}>
                {formatDateRange(stop.period.startDate, stop.period.endDate)}
              </Tooltip>
              <Popup
                maxWidth={250}
                closeButton={false}
                className="leaflet-popup-content-wrapper"
              >
                <div
                  style={{
                    padding: 10,
                    width: "100%",
                    margin: "0px",
                    borderRadius: 6,
                    overflow: "hidden",
                    fontFamily: "sans-serif",
                    boxShadow: "0 4px 12px rgba(0,0,0,0.2)",
                  }}
                >
                  {/* Image with Date Tag */}
                  <div style={{ position: "relative" }}>
                    <img
                      src={stop.images[0]}
                      alt={stop.title.en}
                      style={{
                        width: "100%",
                        height: 130,
                        objectFit: "cover",
                        borderRadius: "6px",
                      }}
                    />
                    <div
                      style={{
                        position: "absolute",
                        top: 8,
                        left: 8,
                        backgroundColor: "white",
                        padding: "4px 8px",
                        borderRadius: 6,
                        fontSize: 12,
                        fontWeight: 500,
                        boxShadow: "0 1px 3px rgba(0,0,0,0.2)",
                      }}
                    >
                      {formatDateRange(
                        stop.period.startDate,
                        stop.period.endDate
                      )}
                    </div>

                    <Image
                      src="/images/itinerary/zoom.svg"
                      alt="Marker"
                      width={26}
                      height={26}
                      className="absolute top-2 right-2 cursor-pointer"
                      onClick={() => {
                        setSelectedLocation(stop);
                        setSelectedImageIndex(0);
                      }}
                    />
                  </div>

                  {/* Card Content */}
                  <div style={{ paddingTop: 12 }}>
                    <div
                      style={{
                        fontWeight: 600,
                        fontSize: 16,
                        marginBottom: 6,
                      }}
                    >
                      {stop.title.en}
                    </div>

                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        fontSize: 13,
                        color: "#374151",
                        marginBottom: 6,
                      }}
                    >
                      <Image
                        src="/images/itinerary/person.svg"
                        alt="Person"
                        width={18}
                        height={18}
                        style={{ marginRight: 6 }}
                      />
                      {stop.contactPerson.name.en}
                    </div>

                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        fontSize: 13,
                        color: "#374151",
                        marginBottom: 6,
                      }}
                    >
                      <Image
                        src="/images/itinerary/location.svg"
                        alt="Location"
                        width={18}
                        height={18}
                        style={{ marginRight: 6 }}
                      />
                      {stop.venue.address.en}
                    </div>

                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        fontSize: 13,
                        color: "#f97316",
                        fontWeight: 500,
                      }}
                    >
                      <Image
                        src="/images/itinerary/phone.svg"
                        alt="Phone"
                        width={18}
                        height={18}
                        style={{ marginRight: 6 }}
                      />
                      {stop.contactPerson.phoneNumber}
                    </div>
                  </div>
                </div>
              </Popup>
            </Marker>
          ))}
        </MapContainer>
        {/* Bottom Card Slider */}
        <div className="absolute bottom-0 left-0 right-0 z-[1000] p-4 bg-gradient-to-t from-white via-white/90 to-transparent">
          <div className="flex w-[100%] overflow-x-auto scrollbar-hide pb-2 gap-2 cursor-pointer">
            {itinerary.map((stop) => (
              <ItineraryCard
                key={stop.id}
                stop={stop}
                onZoomClick={() => {
                  setSelectedLocation(stop);
                  setSelectedImageIndex(0);
                }}
              />
            ))}
          </div>
        </div>
      </div>

      {selectedLocation && (
        <div
          className="fixed inset-0 z-50 flex flex-col items-center justify-center bg-black bg-opacity-80 backdrop-blur-sm px-4"
          onClick={handleClose}
        >
          <div
            className="relative w-full max-w-3xl flex flex-col items-center gap-4"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Download button
      <a
        href={selectedLocation.images[selectedImageIndex]}
        download
        target="_blank"
        rel="noopener noreferrer"
        className="self-end mb-2 text-white bg-secondary bg-opacity-60 rounded-full p-3 hover:bg-opacity-80 transition"
      >
        <FaDownload size={18} />
      </a> */}

            <div className="relative w-full flex items-center justify-between gap-20">
              {/* Prev button OUTSIDE left */}
              <button
                onClick={handlePrev}
                disabled={selectedImageIndex === 0}
                className={`rounded-full p-3 transition ${
                  selectedImageIndex === 0
                    ? "bg-secondary text-gray-400 cursor-not-allowed"
                    : "bg-white text-black hover:bg-opacity-80"
                }`}
              >
                <FaChevronLeft size={14} />
              </button>

              {/* Center image */}
              <div className="bg-white rounded-lg overflow-hidden flex-grow max-h-[80vh] flex items-center justify-center">
                {/* <Image
                  src={selectedLocation.images[selectedImageIndex]}
                  alt="Preview"
                  width={1000}
                  height={800}
                  className="object-contain max-h-[80vh] w-full"
                /> */}
                <img
                  src={selectedLocation.images[selectedImageIndex]}
                  alt="Preview"
                  className="object-contain max-h-[60vh] w-full"
                />
              </div>

              {/* Next button OUTSIDE right */}
              <button
                onClick={handleNext}
                disabled={
                  selectedImageIndex === selectedLocation.images.length - 1
                }
                className={`rounded-full p-3 transition ${
                  selectedImageIndex === selectedLocation.images.length - 1
                    ? "bg-secondary text-gray-400 cursor-not-allowed"
                    : "bg-white text-black hover:bg-opacity-80"
                }`}
              >
                <FaChevronRight size={14} />
              </button>
            </div>

            <div className="mt-6 text-gray-100 font-normal text-[16px]">
              <p>{selectedLocation.description.en}</p>
            </div>
            {/* Close button */}
            <button
              onClick={handleClose}
              className="mt-16 text-white bg-secondary bg-opacity-60 rounded-full p-3 hover:bg-opacity-80 transition"
            >
              <FaTimes size={14} />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

//Helper function
function formatDateRange(start: number, end: number) {
  const startDate = new Date(start);
  const endDate = new Date(end);

  return `${startDate.toLocaleDateString("en", {
    day: "2-digit",
    month: "short",
  })} - ${endDate.toLocaleDateString("en", {
    day: "2-digit",
    month: "short",
  })}`;
}

export default Itinerary;
