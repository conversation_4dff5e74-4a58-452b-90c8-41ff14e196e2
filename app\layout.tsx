import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import IndexLayout from "@/components/indexLayout/indexLayout";
import { SidebarContextProvider } from "@/src/context/sidebar.context";
import appConfig from "@/src/config/apps";
import { SearchContextProvider } from "@/src/context/search.context";
import { FilterContextProvider } from "@/src/context/filter.context";
import { BookFilterContextProvider } from "@/src/context/book-filter.context";
import { AudioContextProvider } from "@/src/context/audio.context";
import { VideoContextProvider } from "@/src/context/video.context";
import { IndexedDBProvider } from "@/src/context/indexedDB.context";
import "../styles/filterModal.css";
import "../styles/videoPlayer.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
    title: appConfig.windowTitle,
    description: appConfig.appDescription,
    icons: {
        icon: appConfig.favicon,
        shortcut: appConfig.favicon16,
        apple: appConfig.appleTouchIcon,
    },
};

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html lang="en">
            <head>
                <link rel="icon" href={appConfig.favicon} />
                <link
                    rel="icon"
                    type="image/png"
                    sizes="16x16"
                    href={appConfig.favicon16}
                />
                <link
                    rel="icon"
                    type="image/png"
                    sizes="32x32"
                    href={appConfig.favicon32}
                />
                <link
                    rel="apple-touch-icon"
                    sizes="192x192"
                    href={appConfig.appleTouchIcon}
                />
                <meta name="msapplication-TileColor" content="#da532c" />
            </head>
            <body className={inter.className}>
                <SidebarContextProvider>
                    <FilterContextProvider>
                        <BookFilterContextProvider>
                            <SearchContextProvider>
                                <AudioContextProvider>
                                    <VideoContextProvider>
                                        <IndexedDBProvider>
                                            {/* ThemeProvider is imported dynamically to avoid SSR issues */}
                                            <IndexLayout>{children}</IndexLayout>
                                        </IndexedDBProvider>
                                    </VideoContextProvider>
                                </AudioContextProvider>
                            </SearchContextProvider>
                        </BookFilterContextProvider>
                    </FilterContextProvider>
                </SidebarContextProvider>
            </body>
        </html>
    );
}
