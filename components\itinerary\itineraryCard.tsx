"use client";

import Image from "next/image";

interface ItineraryCardProps {
  stop: {
    id: string;
    title: { en: string };
    images: string[];
    period: { startDate: string; endDate: string };
    contactPerson: { name: { en: string }; phoneNumber: string };
    venue: { address: { en: string } };
  };
  onZoomClick: () => void;
}

const ItineraryCard = ({ stop, onZoomClick }: ItineraryCardProps) => {
  return (
    <div className="flex bg-white rounded-xl shadow-md h-[100px] overflow-hidden mr-4 flex-shrink-0">
      {/* Image Side */}
      <div className="relative h-full aspect-[7/8] flex-shrink-0 p-2 ">
        <img
          src={stop.images[0]}
          alt={stop.title.en}
          className="w-full h-full object-cover rounded-lg"
        />
        <Image
          src="/images/itinerary/zoom.svg"
          alt="Zoom"
          width={24}
          height={24}
          onClick={onZoomClick}
          className="absolute top-3 right-3 cursor-pointer"
        />
      </div>

      {/* Content Side */}
      <div className="pr-4 p-2 flex flex-col justify-center text-sm text-gray-800 whitespace-nowrap">
        <p className="text-base font-semibold leading-5 mb-1">
          {stop.title.en}
        </p>

        <div className="flex items-center gap-2 mb-[4px]">
          <Image
            src="/images/itinerary/person.svg"
            alt="Person"
            width={14}
            height={14}
          />
          <span>{stop.contactPerson.name.en}</span>
        </div>

        <div className="flex items-center gap-2 mb-[4px]">
          <Image
            src="/images/itinerary/location.svg"
            alt="Location"
            width={14}
            height={14}
          />
          <span>{stop.venue.address.en}</span>
        </div>

        <div className="flex items-center gap-2 text-primary font-semibold">
          <Image
            src="/images/itinerary/phone.svg"
            alt="Phone"
            width={14}
            height={14}
          />
          <span>{stop.contactPerson.phoneNumber}</span>
        </div>
      </div>
    </div>
  );
};

export default ItineraryCard;
