"use client";
import React, { useState } from "react";
import Image from "next/image";
import { Poppins } from "next/font/google";
import { FaDownload, FaGreaterThan, FaTimes } from "react-icons/fa";
import { useRouter } from "next/navigation";

const poppins = Poppins({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
});

const dummyPhotos = [
  "/images/about/about1.png",
  "/images/about/about2.png",
  "/images/about/about2-3.png",
  "/images/about/about1-4.png",
  "/images/about/about1-5.png",
  "/images/about/about1-6.png",
  "/images/about/about1-2.png",
  "/images/about/about2-2.png",
  "/images/about/about1-3.png",
  "/images/about/about2-4.png",
];

const Photos = () => {
  const [selectedPhoto, setSelectedPhoto] = useState<string | null>(null);
  const router = useRouter();

  const handleOpen = (src: string) => {
    setSelectedPhoto(src);
  };

  const handleClose = () => {
    setSelectedPhoto(null);
  };

  return (
    <div className={`w-full h-full ${poppins.className} tracking-normal`}>
      {/* Header */}
      <div className="w-full h-[60px] md:h-[84px] flex items-center md:items-end px-4 md:py-3 border-b">
        <button
          onClick={() => router.push("/gallery")}
          className="flex items-center gap-2 text-[24px] md:text-[28px] font-[600] leading-8 text-gray-700 hover:text-gray-900 transition cursor-pointer"
        >
          <span className="text-gray-400">Gallery</span>
          <FaGreaterThan size={18} className="text-gray-400" />
          <span className="text-black">Srirangam Sravanam Kirtanam Camp</span>
        </button>
      </div>

      {/* Photos Grid */}
      <div className="w-full h-[calc(100%-60px)] md:h-[calc(100%-84px)] p-6 overflow-y-auto scrollbar">
        <div className="columns-1 sm:columns-2 lg:columns-4 gap-6 space-y-6">
          {dummyPhotos.map((src, index) => (
            <div
              key={index}
              className="break-inside-avoid overflow-hidden rounded-lg cursor-pointer"
              onClick={() => handleOpen(src)}
            >
              <Image
                src={src}
                alt={`Photo ${index + 1}`}
                width={600}
                height={400}
                className="w-full h-auto object-cover rounded-lg hover:opacity-90 transition"
              />
            </div>
          ))}
        </div>
      </div>

      {/* Modal Overlay */}
      {selectedPhoto && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80 backdrop-blur-sm px-4"
          onClick={handleClose}
        >
          <div
            className="relative max-w-3xl w-full flex flex-col items-center"
            onClick={(e) => e.stopPropagation()}
          >
            <a
              href={selectedPhoto}
              download
              target="_blank"
              rel="noopener noreferrer"
              className="self-end mb-8 sm:mr-20 mr-10 text-accent  bg-opacity-60 rounded-full hover:bg-opacity-60 transition"
            >
              <FaDownload size={18} />
            </a>

            <div className="bg-white rounded-lg overflow-hidden w-[80%] h-[70%]">
              <Image
                src={selectedPhoto}
                alt="Preview"
                width={900}
                height={400}
                className="w-full h-full object-cover rounded-lg"
              />
            </div>

            <button
              onClick={handleClose}
              className="mt-10 text-accent bg-secondary bg-opacity-60 rounded-full p-3 hover:bg-opacity-60 transition"
            >
              <FaTimes size={18} />
            </button>
          </div>
        </div>
      )}

    </div>
  );
};

export default Photos;
